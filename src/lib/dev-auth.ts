/**
 * Development Authentication Bypass
 * 
 * This file provides a simple authentication bypass for development purposes.
 * It allows you to access the dashboard without setting up a full database.
 * 
 * ⚠️ WARNING: This should NEVER be used in production!
 */

export const DEV_ADMIN_CREDENTIALS = {
  email: '<EMAIL>',
  password: 'admin123',
  name: '<PERSON>p<PERSON>olt Admin',
  role: 'admin',
  id: 'dev-admin-001'
};

export const DEV_USERS = [
  DEV_ADMIN_CREDENTIALS,
  {
    email: '<EMAIL>',
    password: 'user123',
    name: 'PropBolt User',
    role: 'user',
    id: 'dev-user-001'
  }
];

export function validateDevCredentials(email: string, password: string) {
  const user = DEV_USERS.find(u => 
    u.email.toLowerCase() === email.toLowerCase() && 
    u.password === password
  );
  
  if (user) {
    return {
      id: user.id,
      email: user.email,
      name: user.name,
      role: user.role,
      emailVerified: true,
      createdAt: new Date(),
      updatedAt: new Date(),
    };
  }
  
  return null;
}

export function createDevSession(user: any) {
  return {
    id: `session-${Date.now()}`,
    userId: user.id,
    expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // 7 days
    token: `dev-token-${user.id}-${Date.now()}`,
    user: user,
  };
}

export const isDevelopment = process.env.NODE_ENV === 'development';
export const useDevAuth = isDevelopment && !process.env.DATABASE_URL;

console.log('🔧 Development Auth Status:', {
  isDevelopment,
  hasDatabase: !!process.env.DATABASE_URL,
  useDevAuth,
});

if (useDevAuth) {
  console.log('⚠️  Using development authentication bypass');
  console.log('📧 Admin credentials:');
  console.log(`   Email: ${DEV_ADMIN_CREDENTIALS.email}`);
  console.log(`   Password: ${DEV_ADMIN_CREDENTIALS.password}`);
}
