'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { useDevAuth } from '@/lib/dev-auth';

interface User {
  id: string;
  email: string;
  name: string;
  role: string;
  emailVerified: boolean;
  createdAt: Date;
  updatedAt: Date;
}

interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signOut: () => void;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function DevAuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    // Check for development user in localStorage
    if (useDevAuth) {
      const devUser = localStorage.getItem('dev-user');
      if (devUser) {
        try {
          const userData = JSON.parse(devUser);
          setUser(userData);
        } catch (error) {
          console.error('Error parsing dev user:', error);
          localStorage.removeItem('dev-user');
        }
      }
    }
    setIsLoading(false);
  }, []);

  const signOut = () => {
    if (useDevAuth) {
      localStorage.removeItem('dev-user');
      setUser(null);
    }
  };

  return (
    <AuthContext.Provider value={{ user, isLoading, signOut }}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within a DevAuthProvider');
  }
  return context;
}
